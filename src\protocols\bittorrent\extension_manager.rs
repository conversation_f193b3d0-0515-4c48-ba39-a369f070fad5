use std::collections::{Hash<PERSON>ap, HashSet};
use std::net::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SocketAddr};
use std::sync::{Arc, Weak};
use std::time::Instant;
use tokio::sync::Mutex;
use tracing::{debug, warn};
use serde_bencode;

use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::config::config_manager::ConfigManager;
use crate::config::constants::DEFAULT_BT_CONNECTION_TIMEOUT;

use crate::protocols::bittorrent::extensions::extension_message_handler::{Extension<PERSON><PERSON>ageH<PERSON><PERSON>, PeerManagerCallback};
use crate::protocols::bittorrent::extensions::extension_protocol::{ExtensionProtocol, ExtensionHandler};
use crate::protocols::bittorrent::extensions::metadata::MetadataExtension;
use crate::protocols::bittorrent::extensions::pex::{PEXExtension, PEXMessage};
// 导入ExtensionMessage trait
use crate::protocols::bittorrent::extensions::message::ExtensionMessageTrait;
// 导入BitTorrentMessage枚举
use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::manager::extension_manager_trait::ExtensionManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
type Result<T> = std::result::Result<T, BitTorrentError>;

/// 扩展管理器，负责处理BitTorrent扩展协议
#[derive(Clone)]
pub struct ExtensionManager {
    /// 扩展协议处理器
    extension_protocol: Option<Arc<Mutex<ExtensionProtocol>>>,
    /// 扩展消息处理器
    extension_message_handler: Option<Arc<Mutex<ExtensionMessageHandler>>>,
    /// 是否启用扩展
    extensions_enabled: bool,
    /// 最后一次PEX交换时间
    last_pex_exchange: Option<Instant>,
    /// 对等点管理器回调
    peer_manager_callback: Option<Weak<dyn PeerManagerCallback>>,
    /// 配置管理器
    config_manager: Option<Arc<ConfigManager>>,
}

impl ExtensionManager {
    /// 创建一个新的扩展管理器
    pub fn new(extensions_enabled: bool) -> Self {
        Self {
            extension_protocol: None,
            extension_message_handler: None,
            extensions_enabled,
            last_pex_exchange: None,
            peer_manager_callback: None,
            config_manager: None,
        }
    }

    /// 初始化扩展
    pub async fn init_extensions(&mut self, peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>, peer_adder: Arc<dyn Fn(SocketAddr) -> Result<()> + Send + Sync>) -> Result<()> {
        if !self.extensions_enabled {
            return Ok(());
        }

        // 创建扩展协议处理器
        let mut extension_protocol = ExtensionProtocol::new();

        // 创建PEX扩展
        let pex_extension = PEXExtension::new(peer_getter, peer_adder);
        extension_protocol.register_handler(Box::new(pex_extension))?;

        // 创建扩展消息处理器
        let extension_protocol_arc = Arc::new(Mutex::new(extension_protocol));
        let extension_message_handler = ExtensionMessageHandler::new(extension_protocol_arc.clone());

        // 保存扩展协议处理器和消息处理器
        self.extension_protocol = Some(extension_protocol_arc);
        self.extension_message_handler = Some(Arc::new(Mutex::new(extension_message_handler)));

        Ok(())
    }

    /// 设置对等点管理器回调
    pub async fn set_peer_manager_callback(&mut self, callback: Weak<dyn PeerManagerCallback>) {
        self.peer_manager_callback = Some(callback.clone());
        
        // 如果扩展消息处理器已初始化，设置回调
        if let Some(handler_mutex) = &self.extension_message_handler {
            let mut handler_guard = handler_mutex.lock().await;
            handler_guard.set_peer_manager_callback(callback);
        }
    }

    /// 处理扩展消息
    pub async fn handle_extension_message(&self, peer_addr: &str, message_id: u8, payload: &[u8]) -> Result<()> {
        if let Some(handler_mutex) = &self.extension_message_handler {
            let handler_guard = handler_mutex.lock().await;
            return handler_guard.handle_message(peer_addr, message_id, payload).await;
        }

        debug!("Extension message handler not initialized, ignoring extension message from {}", peer_addr);
        Ok(())
    }

    /// 注册元数据扩展
    pub async fn register_metadata_extension_object(&mut self, metadata_extension: MetadataExtension) -> anyhow::Result<()> {
        if !self.extensions_enabled {
            return Ok(());
        }

        // 如果已经有扩展协议处理器，直接注册元数据扩展
        if let Some(existing_protocol) = &self.extension_protocol {
            let mut protocol_guard = existing_protocol.lock().await;
            // 直接注册元数据扩展
            protocol_guard.register_handler(Box::new(metadata_extension))?;
        } else {
            // 否则，创建新的处理器
            let mut new_extension_protocol = ExtensionProtocol::new();
            new_extension_protocol.register_handler(Box::new(metadata_extension))?;
            
            self.extension_protocol = Some(Arc::new(Mutex::new(new_extension_protocol)));
            
            // 创建扩展消息处理器
            let extension_message_handler = ExtensionMessageHandler::new(self.extension_protocol.as_ref().unwrap().clone());
            self.extension_message_handler = Some(Arc::new(Mutex::new(extension_message_handler)));
        }

        Ok(())
    }
    
    /// 使用info_hash注册元数据扩展
    pub async fn register_metadata_extension(&mut self, info_hash: [u8; 20]) -> anyhow::Result<()> {
        // 创建元数据扩展
        let metadata_extension = MetadataExtension::new(info_hash);
        
        // 调用原始方法注册元数据扩展
        self.register_metadata_extension_object(metadata_extension).await
    }

    /// 检查是否启用了扩展
    pub fn extensions_enabled(&self) -> bool {
        self.extensions_enabled
    }

    /// 处理待添加的对等点
    pub async fn process_pending_peers(&self, torrent_info: &TorrentInfo, peers: &mut HashMap<String, Arc<Mutex<BitTorrentPeer>>>) -> anyhow::Result<()> {
        // 如果扩展未启用，直接返回
        if !self.extensions_enabled {
            return Ok(());
        }
        
        // 如果扩展消息处理器未初始化，直接返回
        if self.extension_message_handler.is_none() {
            return Ok(());
        }
        
        // 从扩展协议处理器中获取待处理的对等点
        if let Some(protocol) = &self.extension_protocol {
            let protocol_guard = protocol.lock().await;
            
            // 获取PEX扩展中的待处理对等点
            let pending_peers = protocol_guard.get_pending_peers(&torrent_info.info_hash);
            
            // 处理新的对等点
            for peer_info in pending_peers {
                // 将SocketAddr转换为字符串作为键
                let addr_str = peer_info.addr.to_string();
                
                // 检查对等点是否已存在
                if !peers.contains_key(&addr_str) {
                    debug!("从扩展协议添加新对等点: {}", peer_info.addr);
                    
                    // 创建新的对等点并添加到集合中
                    // 从配置管理器获取超时设置
                    let timeout_secs = self.get_connection_timeout();
                    let num_pieces = torrent_info.pieces.len() as u32;
                    
                    // 创建新的对等点
                    let new_peer = BitTorrentPeer::new(
                        peer_info.addr,
                        &torrent_info.info_hash,
                        peer_info.id.unwrap_or_default().as_slice(),
                        timeout_secs,
                        num_pieces,
                        None,
                        Some(torrent_info.clone()),
                        Some(torrent_info.name.clone())
                    ).await.unwrap_or_else(|e| {
                        debug!("创建对等点失败: {}", e);
                        panic!("创建对等点失败: {}", e);
                    });
                    
                    peers.insert(addr_str, Arc::new(Mutex::new(new_peer)));
                }
            }
        }
        
        Ok(())
    }
    
    /// 设置是否启用扩展
    pub fn set_extensions_enabled(&mut self, enabled: bool) {
        self.extensions_enabled = enabled;
    }

    /// 获取扩展协议处理器
    pub fn get_extension_protocol(&self) -> Option<Arc<Mutex<ExtensionProtocol>>> {
        self.extension_protocol.clone()
    }

    /// 获取扩展消息处理器
    pub fn get_extension_message_handler(&self) -> Option<Arc<Mutex<ExtensionMessageHandler>>> {
        self.extension_message_handler.clone()
    }

    /// 获取最后一次PEX交换时间
    pub fn get_last_pex_exchange(&self) -> Option<Instant> {
        self.last_pex_exchange
    }
    
    /// 检查是否有完整的元数据
    pub fn has_complete_metadata(&self) -> bool {
        if let Some(protocol) = &self.extension_protocol {
            // 使用block_on来在同步上下文中执行异步操作
            let protocol_guard = tokio::runtime::Handle::current().block_on(protocol.lock());
            
            // 获取元数据扩展处理器
            if let Some(handler) = protocol_guard.get_handler("ut_metadata") {
                // 尝试将处理器转换为MetadataExtension
                if let Some(metadata_ext) = handler.as_any().downcast_ref::<MetadataExtension>() {
                    // 检查是否有完整的元数据
                    return tokio::runtime::Handle::current().block_on(metadata_ext.get_complete_metadata()).is_some();
                }
            }
        }
        
        false
    }
    
    /// 获取元数据
    pub fn get_metadata(&self) -> Vec<u8> {
        if let Some(protocol) = &self.extension_protocol {
            // 使用block_on来在同步上下文中执行异 asynchronous 操作
            let protocol_guard = tokio::runtime::Handle::current().block_on(protocol.lock());
            
            // 获取元数据扩展处理器
            if let Some(handler) = protocol_guard.get_handler("ut_metadata") {
                // 尝试将处理器转换为MetadataExtension
                if let Some(metadata_ext) = handler.as_any().downcast_ref::<MetadataExtension>() {
                    // 获取完整的元数据
                    if let Some(metadata) = tokio::runtime::Handle::current().block_on(metadata_ext.get_complete_metadata()) {
                        return metadata;
                    }
                }
            }
        }
        
        Vec::new()
    }
    
    /// 获取元数据大小
    pub fn get_metadata_size(&self) -> usize {
        if let Some(protocol) = &self.extension_protocol {
            // 使用block_on来在同步上下文中执行异步操作
            let protocol_guard = tokio::runtime::Handle::current().block_on(protocol.lock());
            
            // 获取元数据扩展处理器
            if let Some(handler) = protocol_guard.get_handler("ut_metadata") {
                // 尝试将处理器转换为MetadataExtension
                if let Some(metadata_ext) = handler.as_any().downcast_ref::<MetadataExtension>() {
                    // 获取元数据大小
                    if let Some(size) = tokio::runtime::Handle::current().block_on(metadata_ext.metadata_size()) {
                        return size as usize;
                    }
                }
            }
        }
        
        0
    }
    
    /// 设置元数据大小
    pub fn set_metadata_size(&self, size: u32) {
        if let Some(protocol) = &self.extension_protocol {
            // 使用block_on来在同步上下文中执行异步操作
            // 直接使用ExtensionProtocol的set_metadata_size方法
            tokio::runtime::Handle::current().block_on(async {
                if let Err(e) = protocol.lock().await.set_metadata_size(size as u64).await {
                    warn!("Failed to set metadata size: {}", e);
                }
            });
        }
    }
    
    /// 获取缺失的元数据分片
    pub fn get_missing_metadata_pieces(&self) -> Vec<usize> {
        let mut missing_pieces = Vec::new();
        
        if let Some(protocol) = &self.extension_protocol {
            // 使用block_on来在同步上下文中执行异步操作
            let protocol_guard = tokio::runtime::Handle::current().block_on(protocol.lock());
            
            // 获取元数据扩展处理器
            if let Some(handler) = protocol_guard.get_handler("ut_metadata") {
                // 尝试将处理器转换为MetadataExtension
                if let Some(metadata_ext) = handler.as_any().downcast_ref::<MetadataExtension>() {
                    // 获取下一个要请求的分片
                    if let Some(piece) = tokio::runtime::Handle::current().block_on(metadata_ext.next_piece_to_request()) {
                        missing_pieces.push(piece as usize);
                    }
                }
            }
        }
        
        missing_pieces
    }
    
    /// 添加元数据分片
    pub async fn add_metadata_piece(&self, piece: u32, data: &[u8]) -> Result<()> {
        if let Some(protocol) = &self.extension_protocol {
            let protocol_guard = protocol.lock().await;
            // 获取元数据扩展处理器
            if let Some(handler) = protocol_guard.get_handler("ut_metadata") {
                if let Some(metadata_ext) = handler.as_any().downcast_ref::<MetadataExtension>() {
                    let metadata_size = metadata_ext.metadata_size().await;
                    let mut dict = HashMap::new();
                    dict.insert(b"msg_type".to_vec(), serde_bencode::value::Value::Int(1));
                    dict.insert(b"piece".to_vec(), serde_bencode::value::Value::Int(piece as i64));
                    if let Some(size) = metadata_size {
                        dict.insert(b"total_size".to_vec(), serde_bencode::value::Value::Int(size as i64));
                    }
                    let mut payload = serde_bencode::to_bytes(&serde_bencode::value::Value::Dict(dict))
                        .map_err(|e| BitTorrentError::Other(format!("bencode error: {}", e)))?;
                    payload.extend_from_slice(data);
                    if let Err(e) = metadata_ext.handle_message(metadata_ext.message_id(), &payload).await {
                        warn!("Failed to handle metadata piece: {}", e);
                        return Err(BitTorrentError::Other(format!("handle_message error: {}", e)));
                    }
                    debug!("Added metadata piece {}", piece);
                    return Ok(());
                }
            }
        }
        Err(BitTorrentError::Other("Failed to add metadata piece: metadata extension not found".into()))
    }
    
    /// 添加从PEX获取的对等点
    pub fn add_peer_from_pex(&self, addr: &str) {
        if let Some(peer_manager) = self.peer_manager_callback.as_ref().and_then(|weak| weak.upgrade()) {
            // 解析地址
            if let Ok(socket_addr) = addr.parse::<SocketAddr>() {
                // 添加对等点
                debug!("Adding peer from PEX: {}", addr);
                tokio::spawn(async move {
                    if let Err(e) = peer_manager.add_peer_to_queue(socket_addr).await {
                        warn!("Failed to add peer from PEX: {}", e);
                    }
                });
            } else {
                warn!("Invalid peer address from PEX: {}", addr);
            }
        } else {
            debug!("Cannot add peer from PEX: peer manager callback not available");
        }
    }

    /// 设置最后一次PEX交换时间
    pub fn set_last_pex_exchange(&mut self, time: Instant) {
        self.last_pex_exchange = Some(time);
    }

    /// 处理PEX扩展的对等点交换
    pub async fn process_pex_exchange(&mut self, peers: &HashMap<String, Arc<Mutex<BitTorrentPeer>>>) -> anyhow::Result<()> {
        // 如果扩展未启用，直接返回
        if !self.extensions_enabled {
            return Ok(());
        }
        
        // 如果扩展协议处理器未初始化，直接返回
        if self.extension_protocol.is_none() {
            return Ok(());
        }

        // 获取扩展协议处理器
        let extension_protocol = self.extension_protocol.as_ref().unwrap();
        let protocol_guard = extension_protocol.lock().await;
        
        // 获取PEX扩展处理器
        let pex_extension = protocol_guard.get_handler("ut_pex");
        if let Some(handler) = pex_extension {
            // 尝试将处理器转换为PEXExtension
            if let Some(pex) = handler.as_any().downcast_ref::<PEXExtension>() {
                // 检查是否应该交换对等点信息
                if pex.should_exchange().await {
                    debug!("执行PEX对等点交换");
                    
                    // 收集当前所有对等点的地址
                    let mut current_peers = Vec::new();
                    for peer in peers.values() {
                        let peer_guard = peer.lock().await;
                        current_peers.push(peer_guard.connection.common.peer_info.addr);
                    }
                    
                    // 创建一个新的peer_getter函数，它会返回我们刚刚收集的对等点列表
                    // 生成PEX消息
                    // 注意：我们不再尝试更新原始PEXExtension的peer_getter字段
                    // 而是直接使用我们收集的对等点列表
                    let current_peers_set: HashSet<SocketAddr> = current_peers.into_iter().collect();
                    
                    // 计算新增的对等点（所有当前对等点都被视为新增）
                    let added: Vec<SocketAddr> = current_peers_set.iter().cloned().collect();
                    
                    // 构建PEX消息
                    let mut message = PEXMessage {
                        added: Vec::new(),
                        added_f: Vec::new(),
                        added6: Vec::new(),
                        added6_f: Vec::new(),
                        dropped: Vec::new(),
                        dropped6: Vec::new(),
                    };
                    
                    // 处理新增的对等点
                    for addr in added {
                        match addr.ip() {
                            IpAddr::V4(ipv4) => {
                                // 添加IPv4地址
                                message.added.extend_from_slice(&ipv4.octets());
                                message.added.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                                // 添加标志 (默认为可达，0x08)
                                message.added_f.push(0x08);
                            },
                            IpAddr::V6(ipv6) => {
                                // 添加IPv6地址
                                message.added6.extend_from_slice(&ipv6.octets());
                                message.added6.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                                // 添加标志 (默认为可达，0x08)
                                message.added6_f.push(0x08);
                            },
                        }
                    }
                    
                    // 释放protocol_guard，以便后面可以修改self
                    drop(protocol_guard);
                    
                    // 更新最后交换时间
                    self.set_last_pex_exchange(Instant::now());
                    
                    // 向所有支持PEX的对等点发送PEX消息
                    for peer in peers.values() {
                        // 修复：直接使用peer_guard，而不是将其作为Result处理
                        let peer_guard = peer.lock().await;
                        // 检查对等点是否支持PEX扩展
                        if let Some(pex_id) = peer_guard.state.extension_handler.extension_map.get("ut_pex") {
                            // 克隆pex_id，以便可以释放peer_guard
                            let pex_id_value = *pex_id;
                            let encoded_message = message.encode()?;
                            // 重新获取可变引用以发送消息
                            drop(peer_guard);
                            let mut peer_guard = peer.lock().await;
                            if let Err(e) = peer_guard.send_bt_message(BitTorrentMessage::Extended(pex_id_value, encoded_message)).await {
                                warn!("向对等点发送PEX消息失败: {}", e);
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 设置配置管理器
    pub fn set_config_manager(&mut self, config_manager: Arc<ConfigManager>) {
        self.config_manager = Some(config_manager);
    }
    
    /// 获取连接超时设置
    fn get_connection_timeout(&self) -> u64 {
        if let Some(config_manager) = &self.config_manager {
            // 获取设置
            let settings = tokio::runtime::Handle::current().block_on(config_manager.get_settings());
            if let Some(bt_config) = &settings.bittorrent {
                return bt_config.connection_timeout;
            }
        }
        // 如果无法获取配置或配置中没有BitTorrent设置，则使用默认值
        DEFAULT_BT_CONNECTION_TIMEOUT
    }
}

#[async_trait::async_trait]
impl ExtensionManagerTrait for ExtensionManager {
    async fn init_extensions(&mut self, peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>, peer_adder: Arc<dyn Fn(SocketAddr) -> Result<()> + Send + Sync>) -> Result<()> {
        self.init_extensions(peer_getter, peer_adder).await
    }
    async fn set_peer_manager_callback(&mut self, callback: Weak<dyn Send + Sync>) {
        // Since we can't safely downcast Weak<dyn Send + Sync> to Weak<dyn PeerManagerCallback>,
        // we'll store the generic callback and handle the conversion when needed.
        // This is a limitation of the current trait design.

        // For now, we'll just ignore this callback since we can't use it safely
        // The proper solution would be to fix the trait to use Weak<dyn PeerManagerCallback>
        // but that would require changing the trait interface.

        // Log that we received a callback but can't use it
        tracing::debug!("Received generic callback in set_peer_manager_callback, but cannot safely convert to PeerManagerCallback");
    }
    async fn handle_extension_message(&self, peer_addr: &str, message_id: u8, payload: &[u8]) -> Result<()> {
        self.handle_extension_message(peer_addr, message_id, payload).await.map_err(BitTorrentError::from)
    }
    async fn register_metadata_extension_object(&mut self, info_hash: [u8; 20]) -> Result<()> {
        self.register_metadata_extension(info_hash).await.map_err(BitTorrentError::from)
    }
    async fn process_pending_peers(&self, torrent_info: &TorrentInfo, peers: &mut HashMap<String, Arc<Mutex<BitTorrentPeer>>>) -> Result<()> {
        self.process_pending_peers(torrent_info, peers).await.map_err(BitTorrentError::from)
    }
}