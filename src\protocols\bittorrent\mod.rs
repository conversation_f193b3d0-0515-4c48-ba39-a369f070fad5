//! BitTorrent模块统一入口

pub mod protocol;
pub mod piece;
pub mod block;
pub mod utils;

// 兼容老路径，逐步迁移
pub mod torrent;
pub mod tracker;
pub mod factory;
pub mod dht;
pub mod piece_manager;
pub mod peer_manager;
pub mod block_manager;
pub mod piece_selector;
pub mod extensions;
pub mod message;
pub mod message_processor;
pub mod piece_request_manager;
pub mod peer_statistics;
pub mod peer_quality;
pub mod extension_manager;
pub mod security;
pub mod downloader;
pub mod handshake;
pub mod extension_handler;
pub mod upload_manager;
pub mod block_collector;
pub mod fast_extension;
pub mod file_manager;
pub mod dht_manager;
pub mod tracker_manager;
pub mod peer;
pub mod stats_manager;
pub mod webseed;
pub mod peer_impl;
pub mod piece_factory;

// 重新导出主要类型
pub use protocol::*;
pub use piece::*;
pub use block::*;
pub use utils::*;
// 兼容老接口
pub use protocol::BitTorrentProtocol;
pub use factory::BitTorrentFactory;
pub use downloader::BitTorrentDownloader;
pub use peer_impl::BitTorrentPeer;
pub use crate::protocols::bittorrent::piece_manager_trait::*;
